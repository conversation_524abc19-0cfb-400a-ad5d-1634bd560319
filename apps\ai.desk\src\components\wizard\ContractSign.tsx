import { Cna<PERSON>utton } from "@code.8cent/react/components";
import {
    Box,
    Checkbox,
    Group,
    LoadingOverlay,
    Stack,
    Progress,
    Text,
} from "@mantine/core";
import { useMemoizedFn, useMount, useRequest, useUnmount } from "ahooks";
import { useEffect, useState, createContext, useContext } from "react";
import {
    SignatureData,
    useSignatureModal,
} from "@code.8cent/react/SignatureModal";
import { useEventBus } from "@/utils/eventBus";
import useWizardStore from "@code.8cent/store/wizard";
import { getFileByUrl, Reader } from "@code.8cent/react/FileViewer";
import { useCountDown } from "@code.8cent/react/hooks";
import { cnaRequest } from "@code.8cent/utils";
import noty from "@code.8cent/react/noty";
import React from "react";
import { DownloadSimple } from "@phosphor-icons/react";
import useSettingStore from "@code.8cent/store/setting";
import { useNavigate } from "react-router-dom";
import api from "@/apis";

// 检测是否为移动端浏览器
const isMobileBrowser = () => {
    const ua = navigator.userAgent.toLowerCase();
    return /mobile|android|iphone|ipad|phone/i.test(ua);
};

type TContractSignCtx = {
    getContractFile: (url: string, type?: string) => void | null;
    setGetContractFileFn: (fn: Function) => void;
    downloadContract: () => void;
    setDownloadContractFn: (fn: Function) => void;
    contractUrl: string | null;
    isDownloading: boolean;
};

const initialContextValue: TContractSignCtx = {
    getContractFile: null,
    setGetContractFileFn: (fn) => {},
    downloadContract: () => {},
    setDownloadContractFn: (fn) => {},
    contractUrl: null,
    isDownloading: true,
};

const SignContractContext =
    createContext<TContractSignCtx>(initialContextValue);

const ContractFile = React.memo(
    ({
        setIsDownloading,
    }: {
        setIsDownloading: (loading: boolean) => void;
    }) => {
        const { setGetContractFileFn, setDownloadContractFn, contractUrl } =
            useContext(SignContractContext);

        const { isIOS, isWechat, isSafari } = useSettingStore();

        const [downloadProgress, setDownloadProgress] = useState(0);
        const [downloadError, setDownloadError] = useState<string | null>(null);

        const {
            run: getContractFile,
            loading: fetching,
            data: contractFile = null,
            mutate,
        } = useRequest(

            async (url: string, type?: string) => {
                if (type === "image") {
                    // 此时 url 是服务器资源路由，转换为 File 对象
                    try {
                        mutate(null);
                        setIsDownloading(true);
                        setDownloadProgress(0);
                        setDownloadError(null);

                        const response = await fetch(url);
                        if (!response.ok) {
                            throw new Error(
                                `HTTP error! status: ${response.status}`
                            );
                        }
                        const blob = await response.blob();
                        const fileName =
                            url.split("/").pop() || "contract-image";
                        const fileExtension = blob.type.split("/")[1] || "jpg";
                        const imageFile = new File(
                            [blob],
                            `${fileName}.${fileExtension}`,
                            {
                                type: blob.type,
                            }
                        );
                        setIsDownloading(false);
                        return imageFile;
                    } catch (error) {
                        console.error(
                            "Failed to convert image URL to File:",
                            error
                        );
                        const errorMsg = "获取图片文件失败";
                        noty.error(errorMsg);
                        setIsDownloading(false);
                        setDownloadError(errorMsg);
                        return null;
                    }
                }

                try {
                    mutate(null);
                    setIsDownloading(true);
                    setDownloadProgress(0);
                    setDownloadError(null);

                    let __file = await getFileByUrl(url, (progressEvent) => {
                        if (progressEvent.lengthComputable) {
                            const percentCompleted = Math.round(
                                (progressEvent.loaded * 100) /
                                    progressEvent.total
                            );
                            setDownloadProgress(percentCompleted);
                        }
                    });

                    if (!__file) {
                        const errorMsg = "获取文件失败";
                        noty.error(errorMsg);
                        setDownloadError(errorMsg);
                    }

                    setIsDownloading(false);
                    return __file;
                } catch (err) {
                    console.log(err);
                    const errorMsg = "下载文件时发生错误";
                    setDownloadError(errorMsg);
                    setIsDownloading(false);
                }
            },
            {
                manual: true,
            }
        );

        const { run: downloadContract } = useRequest(
            async () => {
                try {
                    // 如果有直接的文件URL，优先使用直接下载
                    if (contractUrl) {
                        // 检测环境
                        const isWechatClient = isWechat;
                        const isMobile = isMobileBrowser();
                        const isSafariBrowser = isSafari;

                        // 微信客户端的特殊处理
                        if (isWechatClient) {
                            // 微信浏览器直接使用 window.open 打开文件URL
                            window.open(contractUrl, "_blank");
                            return;
                        }

                        // 移动端Safari或iOS的特殊处理
                        if ((isMobile && isSafariBrowser) || isIOS === true) {
                            window.location.href = contractUrl;
                            return;
                        }

                        // 其他移动端浏览器
                        if (isMobile) {
                            const newWindow = window.open(
                                contractUrl,
                                "_blank"
                            );
                            if (!newWindow) {
                                // 如果新窗口被阻止，回退到直接跳转
                                window.location.href = contractUrl;
                            }
                            return;
                        }

                        // 桌面端浏览器，使用传统的下载方式
                        const link = document.createElement("a");
                        link.href = contractUrl;
                        link.download = `全球合伙人大联盟中国区域合伙人加盟合同.pdf`;
                        link.target = "_blank"; // 在新窗口打开，避免页面跳转
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        return;
                    }

                    // 如果没有直接URL，回退到原来的Blob方式（兼容性处理）
                    if (contractFile instanceof File === true) {
                        // 检测环境
                        const isWechatClient = isWechat;
                        const isMobile = isMobileBrowser();
                        const isSafariBrowser = isSafari;

                        // 微信客户端的特殊处理
                        if (isWechatClient) {
                            // 微信浏览器不支持 blob URL 下载，需要特殊处理
                            try {
                                // 方法1: 尝试使用 window.open
                                const blob = new Blob([contractFile], {
                                    type: "application/pdf",
                                });
                                const url = window.URL.createObjectURL(blob);

                                const newWindow = window.open(url, "_blank");

                                setTimeout(() => {
                                    window.URL.revokeObjectURL(url);
                                }, 3000);

                                if (!newWindow) {
                                    // 如果新窗口被阻止，尝试方法2
                                    throw new Error("window.open 被阻止");
                                }
                            } catch (openError) {
                                console.log(
                                    "window.open 失败，尝试其他方法:",
                                    openError
                                );

                                // 方法2: 尝试使用 location.href
                                try {
                                    const blob = new Blob([contractFile], {
                                        type: "application/pdf",
                                    });
                                    const url =
                                        window.URL.createObjectURL(blob);

                                    // 显示用户提示
                                    noty.error(
                                        "微信浏览器限制下载，请点击右上角菜单选择'在浏览器中打开'"
                                    );

                                    // 延迟跳转，给用户时间看到提示
                                    setTimeout(() => {
                                        window.location.href = url;
                                        setTimeout(() => {
                                            window.URL.revokeObjectURL(url);
                                        }, 2000);
                                    }, 2000);
                                } catch (hrefError) {
                                    console.log(
                                        "location.href 失败:",
                                        hrefError
                                    );
                                    noty.error(
                                        "微信浏览器限制下载，请点击右上角菜单选择'在浏览器中打开'"
                                    );
                                }
                            }
                            return;
                        }

                        // 移动端Safari或iOS的特殊处理
                        if ((isMobile && isSafariBrowser) || isIOS === true) {
                            const url =
                                window.URL.createObjectURL(contractFile);
                            window.location.href = url;
                            // 延迟释放URL，确保跳转完成
                            setTimeout(() => {
                                window.URL.revokeObjectURL(url);
                            }, 2000);
                            return;
                        }

                        // 其他移动端浏览器
                        if (isMobile) {
                            const url =
                                window.URL.createObjectURL(contractFile);
                            const newWindow = window.open(url, "_blank");
                            if (!newWindow) {
                                // 如果新窗口被阻止，回退到直接跳转
                                window.location.href = url;
                            }
                            // 延迟释放URL
                            setTimeout(() => {
                                window.URL.revokeObjectURL(url);
                            }, 2000);
                            return;
                        }

                        // 桌面端浏览器，使用传统的下载方式
                        const url = window.URL.createObjectURL(contractFile);
                        const link = document.createElement("a");
                        link.href = url;
                        link.download = `全球合伙人大联盟中国区域合伙人加盟合同.pdf`;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        window.URL.revokeObjectURL(url);
                    }
                } catch (err) {
                    console.log(err);
                    noty.error("下载失败，请重试");
                }
            },
            {
                manual: true,
            }
        );

        useEffect(() => {
            if (typeof getContractFile === "function") {
                setGetContractFileFn(() => getContractFile);
            }
        }, [getContractFile]);

        useEffect(() => {
            if (typeof downloadContract === "function") {
                setDownloadContractFn(() => downloadContract);
            }
        }, [downloadContract]);

        return (
            <Box className="tw-flex-1 tw-relative">
                <LoadingOverlay visible={fetching} />
                {/* {fetching ? (
                    <>
                        <LoadingOverlay />
                        {downloadProgress > 0 && (
                            <Box className="tw-absolute tw-top-1/2 tw-transform tw-translate-y-[-50%] tw-left-4 tw-right-4 tw-z-50">
                                <Stack gap="xs">
                                    <Text
                                        size="sm"
                                        c="blue"
                                    >
                                        正在下载合同文件...
                                    </Text>
                                    <Progress
                                        value={downloadProgress}
                                        size="md"
                                    />
                                    <Text
                                        size="xs"
                                        c="dimmed"
                                    >
                                        {downloadProgress}%
                                    </Text>
                                </Stack>
                            </Box>
                        )}
                    </>
                ) : null} */}
                {downloadError && (
                    <Box className="tw-absolute tw-top-1/2 tw-transform tw-translate-y-[-50%] tw-left-4 tw-right-4 tw-z-50">
                        <Stack
                            gap="xs"
                            align="center"
                        >
                            <Text
                                size="sm"
                                c="red"
                            >
                                下载失败
                            </Text>
                            <Text
                                size="xs"
                                c="dimmed"
                            >
                                {downloadError}
                            </Text>
                            <CnaButton
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                    setDownloadError(null);
                                    if (contractUrl) {
                                        getContractFile(contractUrl);
                                    }
                                }}
                            >
                                重新下载
                            </CnaButton>
                        </Stack>
                    </Box>
                )}
                {contractFile ? (
                    <Reader documentImages={[contractFile]} />
                ) : null}
            </Box>
        );
    }
);

const ContractSignPanel = ({
    disabled,
    setContractUrl,
}: {
    disabled?: boolean;
    setContractUrl: (url: string | null) => void;
}) => {
    const { getContractFile, downloadContract, contractUrl, isDownloading } =
        useContext(SignContractContext);

    const navigate = useNavigate();

    const { open } = useSignatureModal();

    const { start, totalSeconds } = useCountDown();

    const { state, setState } = useWizardStore();

    const [signData, setSignData] = useState<SignatureData>([]);

    const [signed, setSigned] = useState(false);

    const [checked, setChecked] = useState(false);

    const [agreementChecked, setAgreementChecked] = useState(false);

    const bus = useEventBus();

    const { run: submitSign, loading: submitting } = useRequest(
        async (signature: string) => {
            const { result, error } = await cnaRequest(
                "/api/v1/profileContract/create",
                "POST",
                {
                    signature,
                    type: 3,
                }
            );

            if (error) {
                noty.error(error.message);
            } else {
                setSigned(true);
                noty.success("签署成功");
                setContractUrl(result.data.file_full_url);

                // 获取已经签署文件图片
                const res = await api.user.getContractFileInfo();
                const contractRes = res.filter((item) => item.type === 3);

                getContractFile(
                    // `${window.api_base_url}/api/v1/profileContract/previewContractTemplate/${result.data.token}`
                    `${window.api_base_url}/storage/${contractRes[0].contract_pic_sig}`,
                    "image"
                );
                bus.emit("wizard.submit.disabled", false);
                let registerSetting = (await window.localForage.getItem(
                    "register-setting"
                )) as string[];

                if (registerSetting.includes("profileContract")) {
                    registerSetting = registerSetting.filter(
                        (item) => item !== "profileContract"
                    );
                    await window.localForage.setItem(
                        "register-setting",
                        registerSetting
                    );
                }
            }
        },
        { manual: true }
    );

    const toNext = async () => {
        const registerSetting = (await window.localForage.getItem(
            "register-setting"
        )) as string[];
        if (registerSetting.length === 0) {
            const userInfo = await api.user.getUserProfile();
            if (userInfo.is_simple === 1) {
                navigate("/member3/index");
            } else {
                navigate("/member/index");
            }
        } else {
            setState(state + 1);
        }
    };

    const { run: getPDFTemplate } = useRequest(
        async () => {
            const { result, error } = await cnaRequest<string>(
                "/api/v1/profileContract/generateContractTemplateToken",
                "POST",
                {
                    type: 3,
                }
            );

            bus.emit("wizard.submit.disabled", true);

            if (!error) {
                // 获取未签署合同文件图片
                const res = await api.user.getContractFileInfo();
                const contractRes = res.filter((item) => item.type === 3);

                // 设置展示的图片
                getContractFile(
                    // `${window.api_base_url}/api/v1/profileContract/previewContractTemplate/${result.data}`
                    `${window.api_base_url}/storage/${contractRes[0].contract_pic}`,
                    "image"
                );
            }
        },
        {
            manual: false,
            ready: typeof getContractFile === "function",
        }
    );

    useMount(() => {
        // 只有在不下载时才开始倒计时
        // if (!isDownloading) {
        //     start(15);
        // }

        bus.emit("wizard.submit.disabled", true);

        bus.on("wizard.submit.click", toNext);
    });

    useEffect(() => {
        if (!isDownloading) {
            setTimeout(() => {
                start(15);
            }, 1000);
        }
    }, [isDownloading]);

    useUnmount(() => {
        bus.off("wizard.submit.click", toNext);
    });

    const openSignatureModal = useMemoizedFn(() => {
        open({
            onConfirm: async ({ data, svg, base64 }) => {
                setSignData(data);
                submitSign(svg);
            },
            signatureData: signData,
        });
    });

    return (
        <Stack>
            <Checkbox
                label="本人已详细阅读并同意签署《全球合伙人大联盟中国区域合伙人加盟合同》。"
                checked={checked}
                onChange={(event) => {
                    // 只有在未签署且不在下载时才允许改变复选框状态
                    if (!signed && !isDownloading) {
                        setChecked(event.currentTarget.checked);
                    }
                }}
                disabled={signed || isDownloading} // 签署后或下载时禁用复选框
            />
            <Checkbox
                label="本人确定上述填写的各项个人信息正确无误。本人也将如实填写后续 C&A 中国所需的其他信息。若因个人提供的信息错误而导致的一切损失将由本人承担。"
                checked={agreementChecked}
                onChange={(event) => {
                    // 只有在未签署且不在下载时才允许改变复选框状态
                    if (!signed && !isDownloading) {
                        setAgreementChecked(event.currentTarget.checked);
                    }
                }}
                disabled={signed || isDownloading} // 签署后或下载时禁用复选框
            />
            <Group justify="center">
                {signed === true ? (
                    <CnaButton
                        leftSection={<DownloadSimple />}
                        onClick={downloadContract}
                        disabled={isDownloading}
                    >
                        下载已签署合同
                    </CnaButton>
                ) : (
                    // <CnaButton disabled>已签署合同</CnaButton>
                    <CnaButton
                        onClick={openSignatureModal}
                        disabled={
                            totalSeconds > 0 ||
                            checked !== true ||
                            agreementChecked !== true ||
                            isDownloading ||
                            submitting
                        }
                        className="tw-h-[max-content] tw-py-2"
                    >
                        <span className="tw-block tw-w-full tw-text-center tw-whitespace-normal tw-break-words tw-leading-[1.1]">
                            {isDownloading ? (
                                "正在下载合同文件..."
                            ) : (
                                <>
                                    确认以上合同内容，并签名{" "}
                                    {totalSeconds > 0
                                        ? `(${totalSeconds})`
                                        : ""}
                                </>
                            )}
                        </span>
                    </CnaButton>
                )}
            </Group>
        </Stack>
    );
};

const ContractSign = () => {
    const [getContractFile, setGetContractFileFn] = useState(null);

    const [downloadContract, setDownloadContractFn] = useState(null);

    const [contractUrl, setContractUrl] = useState<string | null>(null);

    const [isDownloading, setIsDownloading] = useState(true);

    return (
        <SignContractContext.Provider
            value={{
                getContractFile,
                setGetContractFileFn,
                downloadContract,
                setDownloadContractFn,
                contractUrl,
                isDownloading,
            }}
        >
            <Stack h="100%">
                <ContractFile setIsDownloading={setIsDownloading} />
                {/* Reader */}
                {/* <Reader
                documentImages={[
                    "https://signaturely.com/wp-content/uploads/2024/06/resident-tenant-landlord-agreement-1.png",
                    "https://signaturely.com/wp-content/uploads/2024/06/resident-tenant-landlord-agreement-2.png",
                    "https://signaturely.com/wp-content/uploads/2024/06/resident-tenant-landlord-agreement-3.png",
                    "https://signaturely.com/wp-content/uploads/2024/06/resident-tenant-landlord-agreement-4.png",
                    "https://signaturely.com/wp-content/uploads/2024/06/resident-tenant-landlord-agreement-5.png",
                    "https://signaturely.com/wp-content/uploads/2024/06/resident-tenant-landlord-agreement-6.png",
                    "https://gips3.baidu.com/it/u=1821127123,1149655687&fm=3028&app=3028&f=JPEG&fmt=auto?w=720&h=1280",
                    "http://gips3.baidu.com/it/u=100751361,**********&fm=3028&app=3028&f=JPEG&fmt=auto?w=960&h=1280",
                    "http://gips1.baidu.com/it/u=**********,617110073&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960",
                    "http://gips3.baidu.com/it/u=**********,837936650&fm=3028&app=3028&f=JPEG&fmt=auto?w=1024&h=1024"
                ]}
                documentTitle="合同签署文档" ></Reader> */}
                <ContractSignPanel setContractUrl={setContractUrl} />
            </Stack>
        </SignContractContext.Provider>
    );
};

export default ContractSign;
