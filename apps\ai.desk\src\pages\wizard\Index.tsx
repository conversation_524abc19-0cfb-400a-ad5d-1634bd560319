import CnaButton from "@code.8cent/react/components/CnaButton";
import CompleteSetup from "@/components/wizard/CompleteSetup";
import LanguageSetup from "@/components/wizard/LanguageSetup";
import SecuritySetup from "@/components/wizard/SecuritySetup";
import UserSetup from "@/components/wizard/UserSetup";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import useWizardStore from "@code.8cent/store/wizard";
import { useEventBus } from "@/utils/eventBus";
import { Box, Group, Image, Stack, Text, Title } from "@mantine/core";
import { useMemoizedFn, useMount, useRequest, useUnmount } from "ahooks";
import { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import ContractSign from "@/components/wizard/ContractSign";
import InvitePartner from "@/components/wizard/InvitePartner";
import ProfileNRIC from "@/components/wizard/ProfileNRIC";
import { cnaRequest } from "@code.8cent/utils";
import noty from "@code.8cent/react/noty";
import BankAccount from "@/components/wizard/BankAccount";
import AgreementSign from "@/components/wizard/AgreementSign";
import ReadDocuments from "@/components/wizard/ReadDocuments";

const RenderWizardSetting = ({ settingName }: { settingName: string }) => {
    switch (settingName) {
        case "settingLanguage": {
            return <LanguageSetup />;
        }

        case "profilePassword": {
            return <SecuritySetup />;
        }

        case "profileAvatar": {
            return <UserSetup />;
        }

        case "profileNRIC": {
            return <ProfileNRIC />;
        }

        // case "bankAccount": {
        //     return <BankAccount />;
        // }

        case "profileContract": {
            return <ContractSign />;
        }

        case "profileAgreement": {
            return <AgreementSign />;
        }

        case "invitePartner": {
            return <InvitePartner />;
        }

        case "readDocuments": {
            return <ReadDocuments />;
        }

        // case "complete": {
        //     return <CompleteSetup />;
        // }

        default: {
            return null;
        }
    }
};

const AccountWizardPage = () => {
    const bus = useEventBus();

    const lang = useSettingStore.use.lang();

    const navigate = useNavigate();

    const { state, setState, registerSetting, setRegisterSetting } =
        useWizardStore();
    console.log("🚀 ~ Index.tsx:80 ~ AccountWizardPage ~ registerSetting:", registerSetting)

    const [loading, setLoading] = useState(false);

    const [disabled, setDisabled] = useState(true);

    const [settingName, setSettingName] = useState(registerSetting[state]);

    const [btnText, setBtnText] = useState("");

    const submit = useMemoizedFn(() => {
        bus.emit("wizard.submit.click");
    });

    useEffect(() => {
        setSettingName(registerSetting[state]);
    }, [state, registerSetting]);

    useEffect(() => {
        const queryParams = new URLSearchParams(location.search);

        // Get a specific query parameter
        if (queryParams.has("step")) {
            const step = queryParams.get("step");
            console.log(step);
            setSettingName(step);
        } // Replace
    }, [location, registerSetting]);

    const { run } = useRequest(async () => {
        const { result, error } = await cnaRequest<{
            register_setting: string[];
        }>("/api/v1/user/registerSettingNew", "GET", {});

        if (!error) {
            const settings = (result.data?.register_setting ?? []).filter((item) => item !== "bankAccount");
            setRegisterSetting([...settings]);
            // setRegisterSetting([
            //     // "profileContract",
            //     //"profileAgreement",
            //     "readDocuments",
            // ]);
            if (settings.length) {
                await window.localForage.setItem("register-setting", settings);
            }
        }
    });

    useEffect(() => {
        if(registerSetting[state] === "readDocuments"){
            setBtnText("进入")
        }else{
            setBtnText(t("common.next.page", lang));
        }
    }, [state, registerSetting])

    useMount(async () => {
        let token = (await window.localForage.getItem("cna-token")) as string;

        if (!token || !token.length) {
            navigate("/account/login");
            return;
        }

        bus.on("wizard.submitting", setLoading);

        bus.on("wizard.submit.disabled", setDisabled);

        bus.on("wizard.submit.text", setBtnText);
    });

    useUnmount(() => {
        bus.off("wizard.submitting", setLoading);

        bus.off("wizard.submit.disabled", setDisabled);

        bus.off("wizard.submit.text", setBtnText);
    });

    return (
        <div className="tw-flex wizard-container">
            <div className="md:tw-w-[200px] md:tw-flex lg:tw-w-[240px] tw-border-r tw-bg-basic-5 tw-hidden tw-flex-col tw-w-0 tw-transition-all">
                <div className="tw-mb-2 tw-mt-5">
                    <Image
                        src="/images/C&A-Logo-Icon-White.svg"
                        w={60}
                        className="tw-mx-auto tw-my-4"
                        draggable={false}
                    />
                    <Text
                        style={{ letterSpacing: "3px" }}
                        className="tw-text-center tw-mt-3 tw-text-xl tw-text-white tw-tracking-[3px] tw-font-bold"
                    >
                        {/* {t("navigation.logo.title", lang)} */}
                        AI 个人办公室
                    </Text>
                </div>
                <div className="tw-flex-1 tw-py-3 tw-overflow-y-auto">
                    {registerSetting.filter((setting) => setting !== "bankAccount").map((setting, index) => {
                        return (
                            <div
                                className={`tw-flex tw-px-8 tw-py-4 tw-items-center tw-justify-start ${
                                    index === state
                                        ? "tw-text-gray-50 tw-font-bold tw-bg-basic-7"
                                        : "tw-text-gray-300"
                                } tw-cursor-pointer`}
                                key={index}
                            >
                                <Text className="tw-flex-1 tw-text-left tw-ml-6">
                                    {t(`wizard.${setting}`, lang)}
                                </Text>
                            </div>
                        );
                    })}
                </div>
            </div>
            <div className="tw-flex-1 tw-overflow-y-auto tw-overflow-x-hidden">
                <Stack
                    className="tw-bg-white tw-p-2 md:tw-py-2 md:tw-px-24 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0"
                    align="center"
                >
                    <Stack className="tw-w-full tw-min-h-full">
                        {/* <Title order={2}>设置启动</Title>
                        <Text size="sm">请按照以下说明完成您的账户设置。</Text> */}

                        <Box className="tw-shadow-lg tw-border tw-px-6 tw-rounded-sm tw-flex-1">
                            {settingName?.length > 0 && (
                                <Stack
                                    gap={0}
                                    h="100%"
                                >
                                    <Group className="tw-py-4 tw-border-b">
                                        <Title
                                            order={3}
                                            fw="normal"
                                        >
                                            {t(`wizard.${settingName}`, lang)}
                                        </Title>
                                    </Group>

                                    <Box className="tw-py-4 tw-border-b tw-flex-1">
                                        <RenderWizardSetting
                                            settingName={settingName}
                                        />
                                        {/* <RenderWizardSetting settingName={"profilePassword"} /> */}
                                    </Box>

                                    <Group
                                        className="tw-py-4"
                                        justify={
                                            registerSetting[state] ===
                                            "complete"
                                                ? "center"
                                                : "space-between"
                                        }
                                    >
                                        {state !== 0 &&
                                        settingName !== "invitePartner" ? (
                                            // <CnaButton
                                            //     color="basic"
                                            //     miw={100}
                                            //     onClick={() => {
                                            //         setState(state - 1);
                                            //     }}
                                            // >
                                            //     上一步
                                            // </CnaButton>
                                            <Box />
                                        ) : (
                                            <Box />
                                        )}
                                        <CnaButton
                                            color="basic"
                                            miw={100}
                                            onClick={submit}
                                            loading={loading}
                                            disabled={disabled}
                                        >
                                            {btnText}
                                        </CnaButton>
                                    </Group>
                                </Stack>
                            )}
                        </Box>
                    </Stack>
                </Stack>
            </div>
        </div>
    );
};

export default AccountWizardPage;
