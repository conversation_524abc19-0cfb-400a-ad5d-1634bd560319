import { em, Select } from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import {
    ArrowsInSimple,
    ArrowsOut,
    CaretDown,
    CaretUp,
    Minus,
    Plus,
    TextIndent,
} from "@phosphor-icons/react";
import React, { useState, useRef, useEffect } from "react";

interface DocumentReaderProps {
    documentImages: (string | File)[];
    documentTitle?: string;
    className?: string;
}

// Zoom modes
type ZoomMode = "auto" | "actual" | "fitPage" | "fitWidth";

const DocumentReader: React.FC<DocumentReaderProps> = ({
    documentImages,
    documentTitle = "文档阅读器",
    className = "",
}) => {
    const [currentPage, setCurrentPage] = useState(0);
    const [scale, setScale] = useState(1);
    const [scaleWidth, setScaleWidth] = useState(null);
    const [scaleHeight, setScaleHeight] = useState(null);
    const [zoomMode, setZoomMode] = useState<ZoomMode>("fitWidth");
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [showThumbnails, setShowThumbnails] = useState(false);
    const [showFullscreenControls, setShowFullscreenControls] = useState(false);
    const [pageInput, setPageInput] = useState("");
    const contentRef = useRef<HTMLDivElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    const pageInputRef = useRef<HTMLInputElement>(null);
    const pageContainerRef = useRef<HTMLDivElement>(null);
    const imageRef = useRef<HTMLImageElement>(null);
    const controlsTimeoutRef = useRef<NodeJS.Timeout>(null);
    const controlsHoverRef = useRef(false);
    const inputFocusRef = useRef(false);
    // 连续滚动模式：每一页的 ref 以及滚动节流
    const pageRefs = useRef<(HTMLDivElement | null)[]>([]);
    const scrollRaf = useRef<number | null>(null);
    const wheelAccumRef = useRef(0);
    const wheelLockedRef = useRef(false);
    const wheelTimeoutRef = useRef<number | null>(null);
    // 用于监听每页在可视区内的占比，决定当前激活页
    const intersectionObserverRef = useRef<IntersectionObserver | null>(null);
    const pageVisibleRatiosRef = useRef<number[]>([]);
    const isMobile = useMediaQuery(`(max-width: ${em(768)})`);

    const calculateZoom = (str?: string) => {
        console.log("xxx", str);
        if (!contentRef.current || !imageRef.current) return;

        const container = contentRef.current;
        const img = imageRef.current;
        if (!container || !img) return;

        // 保存当前滚动位置
        const scrollTop = container.scrollTop;
        const scrollHeight = container.scrollHeight;
        const clientHeight = container.clientHeight;
        const scrollRatio = scrollHeight > 0 ? scrollTop / scrollHeight : 0;

        const containerWidth = container.clientWidth;
        const containerHeight = container.getBoundingClientRect().height;
        const imgWidth = img.getBoundingClientRect().width / scale;
        const imgHeight = img.getBoundingClientRect().height / scale;

        let newScale = scale;

        switch (zoomMode) {
            case "auto":
                // 自动缩放 - 根据容器大小和图片大小计算最佳比例
                const widthRatio = containerWidth / imgWidth;
                const heightRatio = containerHeight / imgHeight;
                newScale = Math.min(widthRatio, heightRatio);
                break;
            case "actual":
                // 实际大小 - 1:1 比例
                newScale = 1;
                break;
            case "fitPage":
                // 适合页面 - 整个图片在容器内可见
                const pageWidthRatio = containerWidth / imgWidth;
                const pageHeightRatio = containerHeight / imgHeight;
                newScale = Math.min(pageWidthRatio, pageHeightRatio);
                break;
            case "fitWidth":
                // 适合宽度 - 图片宽度匹配容器宽度
                newScale = containerWidth / imgWidth;
                break;
        }

        setScale(Math.max(newScale, 0.25));
        setScaleWidth(Math.max(newScale, 0.25) * imgWidth);
        setScaleHeight(Math.max(newScale, 0.25) * imgHeight);

        // 在下一帧恢复滚动位置
        // requestAnimationFrame(() => {
        //     if (contentRef.current) {
        //         const newScrollHeight = contentRef.current.scrollHeight;
        //         const newScrollTop = scrollRatio * newScrollHeight;
        //         contentRef.current.scrollTop = newScrollTop;
        //     }
        // });
    };

    useEffect(() => {
        const styleElement = document.createElement("style");
        styleElement.innerHTML = styles;
        document.head.appendChild(styleElement);

        return () => {
            document.head.removeChild(styleElement);
        };
    }, []);

    // 处理键盘事件
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === "ArrowLeft") {
                goToPreviousPage();
            } else if (e.key === "ArrowRight") {
                goToNextPage();
            } else if (e.key === "Escape" && isFullscreen) {
                exitFullscreen();
            } else if (e.key === "f" && e.ctrlKey) {
                e.preventDefault();
                toggleFullscreen();
            } else if (e.key === "t" && e.ctrlKey) {
                e.preventDefault();
                toggleThumbnails();
            } else if (e.key === "g" && e.ctrlKey) {
                e.preventDefault();
                pageInputRef.current?.focus();
                pageInputRef.current?.select();
            } else if (e.key === "0" && e.ctrlKey) {
                e.preventDefault();
                setZoomMode("auto");
            } else if (e.key === "1" && e.ctrlKey) {
                e.preventDefault();
                setZoomMode("actual");
            }
        };

        document.addEventListener("keydown", handleKeyDown);
        return () => document.removeEventListener("keydown", handleKeyDown);
    }, [currentPage, isFullscreen, showThumbnails]);

    // 监听全屏变化
    useEffect(() => {
        const handleFullscreenChange = () => {
            const fullscreen = !!document.fullscreenElement;
            setIsFullscreen(fullscreen);
            // 全屏时自动关闭缩略图面板
            if (fullscreen && showThumbnails) {
                setShowThumbnails(false);
            }
            // 进入全屏时显示控制栏3秒
            if (fullscreen) {
                showControlsTemporarily();
            }
        };

        document.addEventListener("fullscreenchange", handleFullscreenChange);
        document.addEventListener(
            "webkitfullscreenchange",
            handleFullscreenChange
        );
        document.addEventListener(
            "mozfullscreenchange",
            handleFullscreenChange
        );
        document.addEventListener("MSFullscreenChange", handleFullscreenChange);

        return () => {
            document.removeEventListener(
                "fullscreenchange",
                handleFullscreenChange
            );
            document.removeEventListener(
                "webkitfullscreenchange",
                handleFullscreenChange
            );
            document.removeEventListener(
                "mozfullscreenchange",
                handleFullscreenChange
            );
            document.removeEventListener(
                "MSFullscreenChange",
                handleFullscreenChange
            );
        };
    }, [showThumbnails]);

    // 全屏时鼠标移动显示控制栏
    useEffect(() => {
        const handleMouseMove = () => {
            if (isFullscreen) {
                showControlsTemporarily();
            }
        };

        if (isFullscreen) {
            document.addEventListener("mousemove", handleMouseMove);
            return () =>
                document.removeEventListener("mousemove", handleMouseMove);
        }
    }, [isFullscreen]);

    // 当当前页面变化时更新页码输入框
    useEffect(() => {
        setPageInput(String(currentPage + 1));
    }, [currentPage]);

    // 计算并应用缩放模式
    useEffect(() => {
        if (!contentRef.current || !imageRef.current) return;

        // 添加延时以确保图片已加载
        const timer = setTimeout(() => calculateZoom("init zoom"), 100);
        return () => clearTimeout(timer);
    }, [zoomMode, currentPage, isFullscreen]);

    // 监听窗口大小变化
    useEffect(() => {
        const handleResize = () => {
            if (
                zoomMode === "auto" ||
                zoomMode === "fitPage" ||
                zoomMode === "fitWidth"
            ) {
                calculateZoom("resize zoom");
                // // 重新计算缩放比例
                // if (!contentRef.current || !imageRef.current) return;
                // const container = contentRef.current;
                // const img = imageRef.current;
                // const containerWidth = container.clientWidth;
                // const containerHeight = container.getBoundingClientRect().height;
                // const imgWidth = img.getBoundingClientRect().width;
                // const imgHeight = img.getBoundingClientRect().height;
                // let newScale = scale;
                // switch (zoomMode) {
                //     case "auto":
                //         const widthRatio = containerWidth / (imgWidth / scale);
                //         const heightRatio = containerHeight / (imgHeight / scale);
                //         newScale = Math.min(widthRatio, heightRatio);
                //         break;
                //     case "fitPage":
                //         const pageWidthRatio = containerWidth / (imgWidth / scale);
                //         const pageHeightRatio = containerHeight / (imgHeight / scale);
                //         newScale = Math.min(pageWidthRatio, pageHeightRatio);
                //         break;
                //     case "fitWidth":
                //         newScale = containerWidth / (imgWidth / scale);
                //         break;
                // }
                // setScale(newScale);
            }
        };

        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, [zoomMode, scale]);

    const showControlsTemporarily = () => {
        if (controlsHoverRef.current || inputFocusRef.current) return; // Don't hide if hovering over controls or input is focused

        setShowFullscreenControls(true);
        if (controlsTimeoutRef.current) {
            clearTimeout(controlsTimeoutRef.current);
        }
        controlsTimeoutRef.current = setTimeout(() => {
            if (!controlsHoverRef.current && !inputFocusRef.current) {
                setShowFullscreenControls(false);
            }
        }, 3000);
    };

    const handleControlsMouseEnter = () => {
        controlsHoverRef.current = true;
        setShowFullscreenControls(true);
        if (controlsTimeoutRef.current) {
            clearTimeout(controlsTimeoutRef.current);
        }
    };

    const handleControlsMouseLeave = () => {
        controlsHoverRef.current = false;
        showControlsTemporarily();
    };

    const handleInputFocus = () => {
        inputFocusRef.current = true;
        setShowFullscreenControls(true);
        if (controlsTimeoutRef.current) {
            clearTimeout(controlsTimeoutRef.current);
        }
    };

    const handleInputBlur = () => {
        inputFocusRef.current = false;
        showControlsTemporarily();
    };

    const goToPreviousPage = () => {
        if (currentPage > 0) {
            goToPage(currentPage - 1);
        }
    };

    const goToNextPage = () => {
        if (currentPage < documentImages.length - 1) {
            goToPage(currentPage + 1);
        }
    };

    const goToPage = (pageIndex: number) => {
        if (pageIndex >= 0 && pageIndex < documentImages.length) {
            setCurrentPage(pageIndex);
            const targetEl = pageRefs.current[pageIndex];
            if (targetEl) {
                targetEl.scrollIntoView({
                    behavior: "smooth",
                    block: "start",
                    inline: "nearest",
                });
            }
            if (window.innerWidth < 768) {
                setShowThumbnails(false);
            }
        }
    };

    const handlePageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        // 只允许输入数字
        const value = e.target.value.replace(/[^0-9]/g, "");
        setPageInput(value);
    };

    const handlePageInputSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (pageInput === "") {
            setPageInput(String(currentPage + 1));
            return;
        }
        const pageNum = parseInt(pageInput, 10);
        let adjustedPageNum = pageNum;
        if (pageNum < 1) {
            adjustedPageNum = 1;
        } else if (pageNum > documentImages.length) {
            adjustedPageNum = documentImages.length;
        }
        setPageInput(String(adjustedPageNum));
        goToPage(adjustedPageNum - 1);
    };

    const zoomIn = () => {
        setScaleWidth((scaleWidth / scale) * Math.min(scale + 0.25, 5));
        setScaleHeight((scaleHeight / scale) * Math.min(scale + 0.25, 5));
        setZoomMode("actual"); // 切换到手动缩放模式
        setScale((prev) => Math.min(prev + 0.25, 5));
    };

    const zoomOut = () => {
        setScaleWidth((scaleWidth / scale) * Math.max(scale - 0.25, 0.25));
        setScaleHeight((scaleHeight / scale) * Math.max(scale - 0.25, 0.25));
        setZoomMode("actual"); // 切换到手动缩放模式
        setScale((prev) => Math.max(prev - 0.25, 0.25));
    };

    const resetZoom = () => {
        setZoomMode("auto");
    };

    const handleZoomModeChange = (value: ZoomMode) => {
        setZoomMode(value);
    };

    const toggleFullscreen = () => {
        if (!document.fullscreenElement) {
            enterFullscreen();
        } else {
            exitFullscreen();
        }
    };

    const toggleThumbnails = () => {
        setShowThumbnails((prev) => !prev);
    };

    const enterFullscreen = () => {
        const elem = containerRef.current;
        if (!elem) return;

        if (elem.requestFullscreen) {
            elem.requestFullscreen();
        } else if ((elem as any).webkitRequestFullscreen) {
            (elem as any).webkitRequestFullscreen();
        } else if ((elem as any).msRequestFullscreen) {
            (elem as any).msRequestFullscreen();
        } else if ((elem as any).mozRequestFullScreen) {
            (elem as any).mozRequestFullScreen();
        }
    };

    const exitFullscreen = () => {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if ((document as any).webkitExitFullscreen) {
            (document as any).webkitExitFullscreen();
        } else if ((document as any).msExitFullscreen) {
            (document as any).msExitFullscreen();
        } else if ((document as any).mozCancelFullScreen) {
            (document as any).mozCancelFullScreen();
        }
    };

    // 基于每页在内容区(reader-content)中的可视高度占比，计算当前激活页
    const computeActivePage = () => {
        const container = contentRef.current;
        if (!container) return;
        const containerRect = container.getBoundingClientRect();

        let bestIndex = -1;
        let bestRatio = 0;

        for (let i = 0; i < pageRefs.current.length; i++) {
            const el = pageRefs.current[i];
            if (!el) continue;
            const rect = el.getBoundingClientRect();
            const visibleTop = Math.max(rect.top, containerRect.top);
            const visibleBottom = Math.min(rect.bottom, containerRect.bottom);
            const visibleHeight = Math.max(0, visibleBottom - visibleTop);
            const ratio = rect.height > 0 ? visibleHeight / rect.height : 0;
            if (ratio > bestRatio) {
                bestRatio = ratio;
                bestIndex = i;
            }
        }

        if (bestIndex !== -1 && bestRatio > 0 && bestIndex !== currentPage) {
            setCurrentPage(bestIndex);
        }
    };

    // 监听内容区滚动，节流到 requestAnimationFrame
    useEffect(() => {
        const el = contentRef.current;
        if (!el) return;

        const onScroll = () => {
            if (scrollRaf.current != null) return;
            scrollRaf.current = requestAnimationFrame(() => {
                computeActivePage();
                scrollRaf.current = null;
            });
        };

        el.addEventListener("scroll", onScroll, { passive: true } as any);
        // 初次进入时计算一次
        computeActivePage();

        return () => {
            el.removeEventListener("scroll", onScroll as any);
            if (scrollRaf.current != null) {
                cancelAnimationFrame(scrollRaf.current);
                scrollRaf.current = null;
            }
        };
        // 仅在文档页数量变化或容器挂载时绑定事件
    }, [documentImages.length]);

    // 缩放或模式变化后，重新计算当前可视页
    useEffect(() => {
        computeActivePage();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [scale, zoomMode]);

    return (
        <div
            className={`document-reader ${className}`}
            ref={containerRef}
        >
            {/* 左侧缩略图导航栏 */}
            {showThumbnails && !isFullscreen && (
                <div className="thumbnail-sidebar">
                    <div className="sidebar-header">
                        <h3>页面导航</h3>
                        <button
                            className="close-sidebar"
                            onClick={toggleThumbnails}
                        >
                            <i className="icon-close">×</i>
                        </button>
                    </div>
                    <div className="thumbnails-container">
                        {documentImages.map((image, index) => {
                            const imageUrl =
                                typeof image === "string"
                                    ? image
                                    : URL.createObjectURL(image);
                            return (
                                <div
                                    key={index}
                                    className={`thumbnail-item ${
                                        index === currentPage ? "active" : ""
                                    }`}
                                    onClick={() => goToPage(index)}
                                >
                                    <img
                                        src={imageUrl}
                                        onLoad={() => {
                                            if (typeof image !== "string") {
                                                URL.revokeObjectURL(imageUrl);
                                            }
                                        }}
                                        alt={`第 ${index + 1} 页`}
                                        className="thumbnail-image"
                                    />
                                    <span className="thumbnail-number">
                                        {index + 1}
                                    </span>
                                </div>
                            );
                        })}
                    </div>
                </div>
            )}

            {/* 顶部导航栏 - 全屏时隐藏 */}
            {!isFullscreen && (
                <div className="reader-header">
                    <div className="header-left">
                        {documentImages.length > 1 && (
                            <button
                                className="sidebar-toggle"
                                onClick={toggleThumbnails}
                            >
                                <TextIndent size={14} />
                                {/* 目录 */}
                            </button>
                        )}

                        {/* 上一页/下一页按钮 */}
                        {documentImages.length > 1 && (
                            <>
                                <button
                                    className="nav-button"
                                    onClick={goToPreviousPage}
                                    disabled={currentPage === 0}
                                >
                                    <CaretUp size={14} />
                                </button>
                                <button
                                    className="nav-button"
                                    onClick={goToNextPage}
                                    disabled={
                                        currentPage ===
                                        documentImages.length - 1
                                    }
                                >
                                    <CaretDown size={14} />
                                </button>
                            </>
                        )}

                        {/* 页码输入框 */}
                        {documentImages.length > 1 && (
                            <form
                                className="page-input-form"
                                onSubmit={handlePageInputSubmit}
                            >
                                <input
                                    ref={pageInputRef}
                                    type="text"
                                    className="page-input"
                                    value={pageInput}
                                    onChange={handlePageInputChange}
                                    onBlur={handlePageInputSubmit}
                                    aria-label="页码输入框"
                                />
                                <span className="page-input-total">
                                    / {documentImages.length}
                                </span>
                            </form>
                        )}
                    </div>
                    {/* <h2 className="document-title">{documentTitle}</h2> */}
                    <div className="header-right">
                        {/* 缩放控制（按钮组） */}
                        <div className="zoom-controls">
                            <button
                                className="zoom-button"
                                onClick={zoomOut}
                                disabled={scale <= 0.25}
                            >
                                <Minus size={14} />
                            </button>
                            {!isMobile && (
                                <span className="zoom-level">
                                    {Math.round(scale * 100)}%
                                </span>
                            )}
                            <button
                                className="zoom-button"
                                onClick={zoomIn}
                                disabled={scale >= 5}
                            >
                                <Plus size={14} />
                            </button>
                            {/* <button className="reset-zoom-button" style={{width: '2rem'}} onClick={resetZoom}>
                重置
              </button> */}
                        </div>

                        {/* 缩放模式选择器（位于全屏按钮之前） */}
                        <div className="zoom-mode-selector tw-h-[max-content]">
                            {/* <select
                value={zoomMode}
                onChange={handleZoomModeChange}
                className="zoom-mode-dropdown text-dark"
                title="缩放模式"
              >
                <option value="auto">自动缩放</option>
                <option value="actual">实际大小</option>
                <option value="fitPage">适合页面</option>
                <option value="fitWidth">适合页宽</option>
              </select> */}
                            <Select
                                value={zoomMode}
                                onChange={handleZoomModeChange}
                                // className="zoom-mode-dropdown text-dark"
                                className="tw-text-dark tw-w-[7rem]"
                                styles={{
                                    input: {
                                        border: "1px solid #ced4da",
                                        borderRadius: 6,
                                    },
                                }}
                                title="缩放模式"
                                data={[
                                    {
                                        value: "auto",
                                        label: "自动缩放",
                                    },
                                    {
                                        value: "actual",
                                        label: "实际大小",
                                    },
                                    {
                                        value: "fitPage",
                                        label: "适合页面",
                                    },
                                    {
                                        value: "fitWidth",
                                        label: "适合页宽",
                                    },
                                ]}
                            />
                        </div>

                        <button
                            className="fullscreen-button"
                            onClick={toggleFullscreen}
                        >
                            <ArrowsOut size={16} />
                            {/* <i className="icon-fullscreen">⤡</i>
              全屏 */}
                        </button>
                    </div>
                </div>
            )}

            {/* 文档内容区域 - 改为连续垂直滚动 */}
            <div
                className={`reader-content ${
                    showThumbnails && !isFullscreen ? "with-sidebar" : ""
                }`}
                ref={contentRef}
            >
                <div
                    className="pages-wrapper"
                    ref={pageContainerRef}
                    // style={{ transform: `scale(${scale})`, transformOrigin: "top center" }}
                >
                    {documentImages.map((imgSrc, index) => {
                        const imageUrl =
                            typeof imgSrc === "string"
                                ? imgSrc
                                : URL.createObjectURL(imgSrc);
                        return (
                            <div
                                key={index}
                                className="page-container page-item"
                                ref={(el) => (pageRefs.current[index] = el)}
                            >
                                <img
                                    ref={index === 0 ? imageRef : undefined}
                                    style={{ width: scaleWidth }}
                                    src={imageUrl}
                                    alt={`第 ${index + 1} 页`}
                                    className="document-page"
                                    onLoad={() => {
                                        if (typeof imgSrc !== "string") {
                                            URL.revokeObjectURL(imageUrl);
                                        }
                                        // 第一张图片加载后，按模式计算缩放
                                        if (
                                            (zoomMode === "auto" ||
                                                zoomMode === "fitPage" ||
                                                zoomMode === "fitWidth") &&
                                            index === 0
                                        ) {
                                            // const calculateZoom = () => {
                                            //     if (!contentRef.current || !imageRef.current) return;
                                            //     const container = contentRef.current;
                                            //     const img = imageRef.current;
                                            //     const containerWidth = container.clientWidth;
                                            //     console.log("🚀 ~ Reader.tsx:671 ~ calculateZoom ~ containerWidth:", containerWidth)
                                            //     const containerHeight = container.getBoundingClientRect().height;
                                            //     console.log("🚀 ~ Reader.tsx:673 ~ calculateZoom ~ containerHeight:", containerHeight)
                                            //     const imgWidth = img.getBoundingClientRect().width;
                                            //     const imgHeight = img.getBoundingClientRect().height;
                                            //     let newScale = scale;
                                            //     switch (zoomMode) {
                                            //         case "auto":
                                            //             {
                                            //                 const widthRatio = containerWidth / (imgWidth / scale);
                                            //                 const heightRatio = containerHeight / imgHeight;
                                            //                 newScale = Math.min(widthRatio, heightRatio);
                                            //             }
                                            //             break;
                                            //         case "fitPage":
                                            //             {
                                            //                 const pageWidthRatio = containerWidth / (imgWidth / scale);
                                            //                 const pageHeightRatio = containerHeight / (imgHeight / scale);
                                            //                 newScale = Math.min(pageWidthRatio, pageHeightRatio);
                                            //             }
                                            //             break;
                                            //         case "fitWidth":
                                            //             newScale = containerWidth / (imgWidth / scale);
                                            //             break;
                                            //     }
                                            //     setScale(newScale);
                                            // };
                                            // setTimeout(() => calculateZoom('select zoom'), 50);
                                        }
                                    }}
                                />
                            </div>
                        );
                    })}
                </div>
            </div>

            {/* 全屏时的浮动控制按钮 */}
            {isFullscreen && (
                <div
                    className={`fullscreen-controls ${
                        showFullscreenControls ? "visible" : "hidden"
                    }`}
                    onMouseEnter={handleControlsMouseEnter}
                    onMouseLeave={handleControlsMouseLeave}
                >
                    <div className="fullscreen-top-bar">
                        <div className="fullscreen-title">
                            {documentTitle} - 第 {currentPage + 1} 页
                        </div>
                        <button
                            className="exit-fullscreen-button"
                            onClick={toggleFullscreen}
                        >
                            {/* <i className="icon-exit-fullscreen">⤢</i> */}
                            <ArrowsInSimple size={14} />
                            退出全屏
                        </button>
                    </div>

                    <div className="fullscreen-bottom-bar">
                        {documentImages.length > 1 && !isMobile && (
                            <div className="fullscreen-nav">
                                <button
                                    className="nav-button"
                                    onClick={goToPreviousPage}
                                    disabled={currentPage === 0}
                                >
                                    {/* <i className="icon-arrow-left">←</i>
                上一页 */}
                                    <CaretUp size={14} />
                                </button>

                                {/* 全屏模式下的页码输入框 */}
                                <form
                                    className="page-input-form"
                                    onSubmit={handlePageInputSubmit}
                                >
                                    {/* <span className="page-input-label">跳至:</span> */}
                                    <input
                                        type="text"
                                        className="page-input"
                                        value={pageInput}
                                        onChange={handlePageInputChange}
                                        onFocus={handleInputFocus}
                                        onBlur={handleInputBlur}
                                        aria-label="页码输入框"
                                    />
                                    <span className="page-input-total">
                                        / {documentImages.length}
                                    </span>
                                </form>

                                <button
                                    className="nav-button"
                                    onClick={goToNextPage}
                                    disabled={
                                        currentPage ===
                                        documentImages.length - 1
                                    }
                                >
                                    {/* 下一页
                <i className="icon-arrow-right">→</i> */}
                                    <CaretDown size={14} />
                                </button>
                            </div>
                        )}

                        <div
                            className="fullscreen-zoom"
                            style={{ width: isMobile ? "max-content" : "" }}
                        >
                            {documentImages.length > 1 && (
                                <>
                                    <button
                                        className="nav-button"
                                        onClick={goToPreviousPage}
                                        disabled={currentPage === 0}
                                    >
                                        {/* <i className="icon-arrow-left">←</i>
                上一页 */}
                                        <CaretUp size={14} />
                                    </button>
                                    <button
                                        className="nav-button"
                                        onClick={goToNextPage}
                                        disabled={
                                            currentPage ===
                                            documentImages.length - 1
                                        }
                                    >
                                        {/* 下一页
                <i className="icon-arrow-right">→</i> */}
                                        <CaretDown size={14} />
                                    </button>
                                </>
                            )}
                            {/* 全屏模式下的缩放模式选择器 - 改为下拉菜单 */}
                            <div className="zoom-mode-selector">
                                {/* <select
                  value={zoomMode}
                  onChange={handleZoomModeChange}
                  className="zoom-mode-dropdown"
                  title="缩放模式"
                  onFocus={handleInputFocus}
                  onBlur={handleInputBlur}
                >
                  <option value="auto">自动缩放</option>
                  <option value="actual">实际大小</option>
                  <option value="fitPage">适合页面</option>
                  <option value="fitWidth">适合页宽</option>
                </select> */}
                                <Select
                                    value={zoomMode}
                                    onChange={handleZoomModeChange}
                                    // className="zoom-mode-dropdown text-dark"
                                    className="tw-text-dark tw-w-[7rem]"
                                    styles={{
                                        input: {
                                            border: "1px solid #ced4da",
                                            borderRadius: 6,
                                        },
                                    }}
                                    title="缩放模式"
                                    comboboxProps={{
                                        zIndex: 9999,
                                        withinPortal: false,
                                    }}
                                    data={[
                                        {
                                            value: "auto",
                                            label: "自动缩放",
                                        },
                                        {
                                            value: "actual",
                                            label: "实际大小",
                                        },
                                        {
                                            value: "fitPage",
                                            label: "适合页面",
                                        },
                                        {
                                            value: "fitWidth",
                                            label: "适合页宽",
                                        },
                                    ]}
                                />
                            </div>

                            <button
                                className="zoom-button"
                                onClick={zoomOut}
                                disabled={scale <= 0.25}
                            >
                                {/* <i className="icon-zoom-out">−</i>
                缩小 */}
                                <Minus size={14} />
                            </button>
                            {!isMobile && (
                                <span className="zoom-level">
                                    {Math.round(scale * 100)}%
                                </span>
                            )}
                            <button
                                className="zoom-button"
                                onClick={zoomIn}
                                disabled={scale >= 5}
                            >
                                {/* <i className="icon-zoom-in">+</i>
                放大 */}
                                <Plus size={14} />
                            </button>
                            {/* <button className="reset-zoom-button" onClick={resetZoom}>
                重置
              </button> */}
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default DocumentReader;

// 示例应用
const App: React.FC = () => {
    const documentImages = [
        "https://signaturely.com/wp-content/uploads/2024/06/resident-tenant-landlord-agreement-1.png",
        "https://signaturely.com/wp-content/uploads/2024/06/resident-tenant-landlord-agreement-2.png",
        "https://signaturely.com/wp-content/uploads/2024/06/resident-tenant-landlord-agreement-3.png",
        "https://signaturely.com/wp-content/uploads/2024/06/resident-tenant-landlord-agreement-4.png",
        "https://signaturely.com/wp-content/uploads/2024/06/resident-tenant-landlord-agreement-5.png",
        "https://signaturely.com/wp-content/uploads/2024/06/resident-tenant-landlord-agreement-6.png",
        "https://gips3.baidu.com/it/u=1821127123,1149655687&fm=3028&app=3028&f=JPEG&fmt=auto?w=720&h=1280",
        "http://gips3.baidu.com/it/u=100751361,1567855012&fm=3028&app=3028&f=JPEG&fmt=auto?w=960&h=1280",
        "http://gips1.baidu.com/it/u=1658389554,617110073&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960",
        "http://gips3.baidu.com/it/u=3419425165,837936650&fm=3028&app=3028&f=JPEG&fmt=auto?w=1024&h=1024",
    ];

    return (
        <div className="app">
            <DocumentReader
                documentImages={documentImages}
                documentTitle="隐私政策"
            />
        </div>
    );
};

// 样式
const styles = `
  // * {
  //   box-sizing: border-box;
  //   margin: 0;
  //   padding: 0;
  // }

  // body {
  //   font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  //   line-height: 1.6;
  //   color: #333;
  //   background-color: #f5f5f5;
  //   padding: 20px;
  // }

  button {
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* IE10+/Edge */
    user-select: none; /* Standard */
  }

  img {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
  pointer-events: none;
  }

  .app {
    max-width: 1000px;
    margin: 0 auto;
  }

  .document-reader {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 80vh;
    position: relative;
  }

  .reader-header {
    padding: 16px 24px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .header-left, .header-right {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .header-right {
    display: flex;
    justify-content: flex-end;
  }

  .document-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
    margin: 0;
    text-align: center;
  }

  .sidebar-toggle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    border: 1px solid #ced4da;
    background-color: #fff;
    color: #495057;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    gap: 6px;
    width: 1rem;
    height: 1rem;
    box-sizing: content-box;
  }

  .sidebar-toggle:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
  }

  .fullscreen-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    border: 1px solid #ced4da;
    background-color: #fff;
    color: #495057;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    gap: 6px;
    width: 1rem;
    height: 1rem;
    box-sizing: content-box;
  }

  .fullscreen-button:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
  }

  /* 缩略图侧边栏 */
  .thumbnail-sidebar {
    position: absolute;
    left: 0;
    top: 0;
    width: 280px;
    height: 100%;
    background-color: #fff;
    border-right: 1px solid #e9ecef;
    z-index: 100;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  }

  .sidebar-header {
    padding: 22px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .sidebar-header h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
    margin: 0;
  }

  .close-sidebar {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .close-sidebar:hover {
    color: #495057;
  }

  .thumbnails-container {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .thumbnail-item {
    position: relative;
    cursor: pointer;
    border: 2px solid transparent;
    border-radius: 6px;
    overflow: hidden;
    transition: all 0.2s ease;
  }

  .thumbnail-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .thumbnail-item.active {
    border-color: #007bff;
  }

  .thumbnail-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
  }

  .thumbnail-number {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    text-align: center;
    padding: 4px;
    font-size: 0.8rem;
  }

  .reader-content {
    flex: 1;
    overflow: auto;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 20px;
    background-color: #f0f2f5;
    transition: margin-left 0.3s ease;
  }

  // .reader-content.with-sidebar {
  //   margin-left: 280px;
  // }

  .pages-wrapper {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .page-container {
    transition: transform 0.2s ease;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .document-page {
    max-width: 100%;
    max-height: 100%;
    box-shadow: 0 2px 16px rgba(0, 0, 0, 0.15);
    object-fit: contain;
  }

  .reader-footer {
    padding: 16px 24px;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
  }

  .footer-left, .footer-right {
    flex: 1;
  }

  .footer-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .page-info {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
  }

  .text-dark {
    border: 1px solid #ced4da !important;
    color: #000000 !important;
    background-color: #fff !important;
  }

  .zoom-mode-dropdown {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 10px 16px;
    border-radius: 8px;
    transition: all 0.2s ease
  }

  .zoom-mode-dropdown:hover {
    border-color: #adb5bd;
  }

  .zoom-mode-dropdown:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }

  /* 页码输入框样式 */
  .page-input-form {
    display: flex;
    align-items: center;
    gap: 6px;
    // margin: 5px 0;
    width: max-content;
  }

  .page-input-label {
    font-size: 0.9rem;
    color: #6c757d;
  }

  .page-input {
    width: 50px;
    padding: 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    text-align: center;
    font-size: 0.9rem;
    height: 1rem;
    box-sizing: content-box;
  }

  .page-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }

  .page-input-total {
    font-size: 0.9rem;
    color: #6c757d;
  }

  .zoom-controls {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .zoom-button, .reset-zoom-button, .nav-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    border: 1px solid #ced4da;
    background-color: #fff;
    color: #495057;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    width: 1rem;
    height: 1rem;
    box-sizing: content-box;
  }

  .zoom-button:hover, .reset-zoom-button:hover, .nav-button:hover:not(:disabled) {
    background-color: #e9ecef;
    border-color: #adb5bd;
  }

  .zoom-button:disabled, .nav-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .zoom-level {
    min-width: 50px;
    text-align: center;
    font-size: 0.9rem;
    font-weight: 500;
  }

  .nav-button {
    gap: 6px;
    padding: 8px;
  }

  .footer-right {
    display: flex;
    justify-content: flex-end;
  }

  /* 全屏样式 */
  .document-reader:fullscreen {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
    background-color: #000;
  }

  .document-reader:-webkit-full-screen {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
    background-color: #000;
  }

  .document-reader:-moz-full-screen {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
    background-color: #000;
  }

  .document-reader:-ms-fullscreen {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
    background-color: #000;
  }

  /* 全屏控制按钮 - 重新设计 */
  .fullscreen-controls {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    pointer-events: none;
    transition: all 0.3s ease;
  }

  .fullscreen-controls.visible {
    opacity: 1;
    transform: translateY(0);
  }

  .fullscreen-controls.hidden {
    opacity: 0;
    transform: translateY(-10px);
    pointer-events: none;
  }

  .fullscreen-top-bar {
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 70%, transparent 100%);
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    pointer-events: auto;
  }

  .fullscreen-title {
    color: white;
    font-size: 1.1rem;
    font-weight: 500;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }

  .exit-fullscreen-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background-color: rgba(255, 255, 255, 0.15);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
    pointer-events: auto;
  }

  .exit-fullscreen-button:hover {
    background-color: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
  }

  .fullscreen-bottom-bar {
    padding: 20px;
    display: flex;
    flex-direction: row;
    gap: 20px;
    align-items: center;
    pointer-events: auto;
    justify-content: center;
    pointer-events: auto;
  }

  .fullscreen-nav, .fullscreen-zoom {
    display: flex;
    align-items: center;
    gap: 12px;
    background-color: rgba(0, 0, 0, 0.6);
    padding: 12px 20px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .fullscreen-nav .nav-button,
  .fullscreen-zoom .zoom-button,
  .fullscreen-zoom .reset-zoom-button {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 8px 14px;
    border-radius: 8px;
    gap: 8px;
    transition: all 0.2s ease;
    width: 1rem;
    height: 1rem;
    box-sizing: content-box;
  }

  .fullscreen-nav .nav-button:hover:not(:disabled),
  .fullscreen-zoom .zoom-button:hover:not(:disabled),
  .fullscreen-zoom .reset-zoom-button:hover:not(:disabled) {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-1px);
  }

  .fullscreen-nav .page-info {
    color: white;
    font-size: 1rem;
    font-weight: 500;
    min-width: 60px;
    margin: 0 15px;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }

  .fullscreen-zoom .zoom-level {
    color: white;
    min-width: 60px;
    font-weight: 500;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }

  /* 全屏模式下的页码输入框 */
  .fullscreen-nav .page-input-form {
    margin: 0 15px;
  }

  .fullscreen-nav .page-input-label {
    color: white;
  }

  .fullscreen-nav .page-input {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
  }

  .fullscreen-nav .page-input:focus {
    border-color: rgba(255, 255, 255, 0.6);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.25);
  }

  .fullscreen-nav .page-input-total {
    color: white;
  }

  /* 全屏模式下的缩放模式选择器 */
  .fullscreen-zoom .zoom-modes {
    display: flex;
    gap: 6px;
    margin-right: 10px;
  }

  .fullscreen-zoom .zoom-mode-button {
    padding: 6px 8px;
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.7rem;
    transition: all 0.2s ease;
  }

  .fullscreen-zoom .zoom-mode-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
  }

  .fullscreen-zoom .zoom-mode-button.active {
    background-color: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.6);
  }

  /* 退出提示 */
  .exit-hint {
    position: fixed;
    top: 80px;
    right: 20px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    animation: fadeInOut 3s ease-in-out;
    pointer-events: none;
  }

  @keyframes fadeInOut {
    0%, 100% { opacity: 0; transform: translateY(-10px); }
    10%, 90% { opacity: 1; transform: translateY(0); }
  }

  @media (max-width: 768px) {
    .thumbnail-sidebar {
      width: 100%;
      z-index: 1000;
    }

    .reader-content.with-sidebar {
      margin-left: 0;
    }

    .thumbnails-container {
      grid-template-columns: repeat(3, 1fr);
    }

    .reader-header {
      flex-direction: column;
      gap: 10px;
    }

    .header-left, .header-right {
      display: flex;
      flex: none;
      align-items: center;
      width: 100%;
    }

    .header-left {
      order: 1;
    }

    .document-title {
      order: 0;
    }

    .header-right {
      order: 2;
      justify-content: center;
    }

    .fullscreen-bottom-bar {
      flex-direction: column;
      gap: 15px;
    }

    /* 移动端适配：全屏底部控制栏布局调整 */
    .fullscreen-nav,
    .fullscreen-zoom {
      width: 100%;
      justify-content: center;
    }
  }
  `;
